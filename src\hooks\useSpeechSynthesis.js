import { useState, useEffect, useRef, useCallback } from 'react';

export const useSpeechSynthesis = () => {
  const [isSupported, setIsSupported] = useState(false);
  const [voices, setVoices] = useState([]);
  const [speaking, setSpeaking] = useState(false);
  const [paused, setPaused] = useState(false);
  const [error, setError] = useState(null);
  const utteranceRef = useRef(null);

  // Check if speech synthesis is supported
  useEffect(() => {
    if ('speechSynthesis' in window) {
      setIsSupported(true);
      
      // Load voices
      const loadVoices = () => {
        const availableVoices = window.speechSynthesis.getVoices();
        setVoices(availableVoices);
      };

      // Load voices immediately
      loadVoices();
      
      // Some browsers load voices asynchronously
      if (window.speechSynthesis.onvoiceschanged !== undefined) {
        window.speechSynthesis.onvoiceschanged = loadVoices;
      }
    } else {
      setIsSupported(false);
      setError('Speech synthesis is not supported in this browser.');
    }
  }, []);

  // Speak function
  const speak = useCallback((text, options = {}) => {
    if (!isSupported) {
      setError('Speech synthesis is not supported.');
      return;
    }

    if (!text.trim()) {
      setError('Please enter some text to speak.');
      return;
    }

    // Cancel any ongoing speech
    window.speechSynthesis.cancel();
    
    const utterance = new SpeechSynthesisUtterance(text);
    utteranceRef.current = utterance;

    // Set voice if specified
    if (options.voice) {
      utterance.voice = options.voice;
    }

    // Set rate, pitch, and volume
    utterance.rate = options.rate || 1;
    utterance.pitch = options.pitch || 1;
    utterance.volume = options.volume || 1;

    // Event handlers
    utterance.onstart = () => {
      setSpeaking(true);
      setPaused(false);
      setError(null);
    };

    utterance.onend = () => {
      setSpeaking(false);
      setPaused(false);
      utteranceRef.current = null;
    };

    utterance.onerror = (event) => {
      setSpeaking(false);
      setPaused(false);
      setError(`Speech synthesis error: ${event.error}`);
      utteranceRef.current = null;
    };

    utterance.onpause = () => {
      setPaused(true);
    };

    utterance.onresume = () => {
      setPaused(false);
    };

    // Start speaking
    window.speechSynthesis.speak(utterance);
  }, [isSupported]);

  // Pause function
  const pause = useCallback(() => {
    if (isSupported && speaking && !paused) {
      window.speechSynthesis.pause();
    }
  }, [isSupported, speaking, paused]);

  // Resume function
  const resume = useCallback(() => {
    if (isSupported && speaking && paused) {
      window.speechSynthesis.resume();
    }
  }, [isSupported, speaking, paused]);

  // Stop function
  const stop = useCallback(() => {
    if (isSupported) {
      window.speechSynthesis.cancel();
      setSpeaking(false);
      setPaused(false);
      utteranceRef.current = null;
    }
  }, [isSupported]);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    isSupported,
    voices,
    speaking,
    paused,
    error,
    speak,
    pause,
    resume,
    stop,
    clearError
  };
};
